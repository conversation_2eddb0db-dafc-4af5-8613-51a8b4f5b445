const express = require('express');
const cors = require('cors');
const path = require('path');
const luamin = require('./luamin');
const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.static('public'));

// Obfuscation utility functions
class LuaObfuscator {
    constructor() {
        this.chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
    }

    // Generate random string
    generateRandomString(length) {
        let result = '';
        for (let i = 0; i < length; i++) {
            result += this.chars.charAt(Math.floor(Math.random() * this.chars.length));
        }
        return result;
    }

    // Generate random variable names
    generateVarName() {
        return this.generateRandomString(Math.floor(Math.random() * 10) + 8);
    }

    // Base64 encode
    base64Encode(str) {
        return Buffer.from(str, 'utf8').toString('base64');
    }

    // XOR encryption
    xorEncrypt(data, key) {
        let result = '';
        for (let i = 0; i < data.length; i++) {
            result += String.fromCharCode(data.charCodeAt(i) ^ key.charCodeAt(i % key.length));
        }
        return result;
    }

    // Convert string to byte array representation
    stringToByteArray(str) {
        const bytes = [];
        for (let i = 0; i < str.length; i++) {
            bytes.push(str.charCodeAt(i));
        }
        return bytes;
    }

    // Generate complex decoder pattern with advanced obfuscation
    generateDecoder(encryptedData, xorKey) {
        const vars = {
            data: this.generateVarName(),
            key: this.generateVarName(),
            result: this.generateVarName(),
            i: this.generateVarName(),
            char: this.generateVarName(),
            decoded: this.generateVarName(),
            base64: this.generateVarName(),
            temp: this.generateVarName(),
            func: this.generateVarName(),
            tbl: this.generateVarName(),
            idx: this.generateVarName(),
            val: this.generateVarName(),
            exec: this.generateVarName()
        };

        const byteArray = this.stringToByteArray(encryptedData);
        const keyArray = this.stringToByteArray(xorKey);

        // Create multiple layers of obfuscation with complex patterns
        const decoderParts = [
            // Part 1: Data and key setup with fake operations
            `local ${vars.tbl} = {${Math.floor(Math.random()*100)}, ${Math.floor(Math.random()*100)}}`,
            `local ${vars.data} = {${byteArray.join(', ')}}`,
            `local ${vars.key} = {${keyArray.join(', ')}}`,
            `${vars.tbl}[1] = ${vars.tbl}[1] + ${vars.tbl}[2]`,

            // Part 2: XOR decryption with obfuscated loop
            `local ${vars.result} = ""`,
            `for ${vars.i} = 1, #${vars.data} do`,
            `    local ${vars.char} = string.char(${vars.data}[${vars.i}] ~ ${vars.key}[((${vars.i} - 1) % #${vars.key}) + 1])`,
            `    ${vars.result} = ${vars.result} .. ${vars.char}`,
            `end`,

            // Part 3: Base64 decoder function with complex logic
            `local function ${vars.func}(${vars.temp})`,
            `    local b = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'`,
            `    ${vars.temp} = string.gsub(${vars.temp}, '[^' .. b .. '=]', '')`,
            `    return (${vars.temp}:gsub('.', function(x)`,
            `        if (x == '=') then return '' end`,
            `        local r, f = '', (b:find(x) - 1)`,
            `        for ${vars.idx} = 6, 1, -1 do`,
            `            r = r .. (f % 2^${vars.idx} - f % 2^(${vars.idx} - 1) > 0 and '1' or '0')`,
            `        end`,
            `        return r`,
            `    end):gsub('%d%d%d?%d?%d?%d?%d?%d?', function(x)`,
            `        if (#x ~= 8) then return '' end`,
            `        local c = 0`,
            `        for ${vars.idx} = 1, 8 do`,
            `            c = c + (x:sub(${vars.idx}, ${vars.idx}) == '1' and 2^(8 - ${vars.idx}) or 0)`,
            `        end`,
            `        return string.char(c)`,
            `    end))`,
            `end`,

            // Part 4: Final execution with additional obfuscation
            `${vars.decoded} = ${vars.func}(${vars.result})`,
            `local ${vars.exec} = load(${vars.decoded})`,
            `${vars.exec}()`
        ];

        // Join all parts with newlines for readability
        return decoderParts.join('\n');
    }

    // Add dummy code injection for confusion
    injectDummyCode() {
        const dummyVars = Array.from({length: 3}, () => this.generateVarName());
        const dummyOps = [
            `local ${dummyVars[0]} = ${Math.floor(Math.random()*1000)}`,
            `local ${dummyVars[1]} = string.char(${Math.floor(Math.random()*26)+65})`,
            `local ${dummyVars[2]} = ${dummyVars[0]} + #${dummyVars[1]}`,
            `${dummyVars[0]} = ${dummyVars[0]} * 2`,
            `if ${dummyVars[2]} > 0 then`,
            `    ${dummyVars[1]} = ${dummyVars[1]} .. ${dummyVars[1]}`,
            `end`
        ];
        return dummyOps.join('\n');
    }

    // Advanced pattern obfuscation
    createComplexPattern(decoder) {
        const wrapperVar = this.generateVarName();
        const execVar = this.generateVarName();
        const dummyCode = this.injectDummyCode();

        // Wrap decoder in complex execution pattern with proper formatting
        return `${dummyCode}\nlocal ${wrapperVar} = function()\n${decoder}\nend\nlocal ${execVar} = ${wrapperVar}\n${execVar}()`;
    }

    // Minify whitespace only (preserve variable and function names)
    minifyWhitespaceOnly(code) {
        return code
            // Remove extra whitespace but preserve single spaces where needed
            .replace(/\s+/g, ' ')
            // Remove spaces around operators and punctuation
            .replace(/\s*([=+\-*/%^<>~!,;(){}[\]])\s*/g, '$1')
            // Remove spaces around dots but be careful with .. operator
            .replace(/\s*\.\s*/g, '.')
            .replace(/\.\./g, ' .. ')
            // Remove leading/trailing whitespace
            .trim()
            // Remove spaces after keywords that don't need them
            .replace(/\b(local|function|if|then|else|elseif|end|for|while|do|repeat|until|return|break|and|or|not|in)\s+/g, '$1 ')
            // Clean up any double spaces
            .replace(/\s{2,}/g, ' ');
    }

    // Main obfuscation function with enhanced complexity
    obfuscate(luaCode) {
        try {
            // Step 1: Remove comments and normalize whitespace
            let cleanCode = luamin.minify(luaCode,{
                renameFunctions: true,
                renameGlobals: false
            })

            // Step 2: Base64 encode
            const base64Encoded = this.base64Encode(cleanCode);

            // Step 3: Generate random XOR key (longer for more security)
            const xorKey = this.generateRandomString(24);

            // Step 4: XOR encrypt the base64 data
            const encrypted = this.xorEncrypt(base64Encoded, xorKey);

            // Step 5: Generate decoder with obfuscated patterns
            const decoder = this.generateDecoder(encrypted, xorKey);

            // Step 6: Add complex execution patterns
            const complexPattern = this.createComplexPattern(decoder);

            // Step 7: Minify the final code (whitespace only, preserve variable names)
            const finalCode = this.minifyWhitespaceOnly(complexPattern);

            return {
                success: true,
                obfuscated: finalCode,
                originalSize: luaCode.length,
                obfuscatedSize: finalCode.length
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }
}

// Create obfuscator instance
const obfuscator = new LuaObfuscator();

// Routes
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.post('/api/obfuscate', (req, res) => {
    const { code } = req.body;
    
    if (!code || typeof code !== 'string') {
        return res.status(400).json({
            success: false,
            error: 'Invalid Lua code provided'
        });
    }

    const result = obfuscator.obfuscate(code);
    res.json(result);
});

// Start server
app.listen(PORT, () => {
    console.log(`Lua Obfuscator server running on http://localhost:${PORT}`);
});

module.exports = app;